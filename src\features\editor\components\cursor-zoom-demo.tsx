import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { calculateZoomScale } from '../utils/zoom-calculations';
import { getCursorPositionAtTime, calculateCursorZoomArea } from '../utils/cursor-utils';
import { CursorOverlayData } from '../../../remotion/CursorOverlay';

/**
 * Demo component to test and showcase cursor zooming functionality
 */
export const CursorZoomDemo: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(1000);
  const [isPlaying, setIsPlaying] = useState(false);

  // Mock cursor data for demonstration
  const mockCursorData: CursorOverlayData = {
    cursorPoints: [
      { frame: 0, time: 0, x: 0.1, y: 0.1, action: 'move' },
      { frame: 15, time: 500, x: 0.3, y: 0.2, action: 'move' },
      { frame: 30, time: 1000, x: 0.5, y: 0.5, action: 'click' },
      { frame: 45, time: 1500, x: 0.7, y: 0.8, action: 'move' },
      { frame: 60, time: 2000, x: 0.9, y: 0.9, action: 'move' },
    ],
    showCursor: true,
    cursorSize: 20,
    cursorColor: '#ff0000',
    showTrail: true,
    trailLength: 10,
    trailOpacity: 0.5,
  };

  const mockZoomConfig = {
    maxZoomScale: 2.0,
    bezierControlPoints: { p1: 0.25, p2: 0.1, p3: 0.25, p4: 1.0 },
    defaultTiming: { startTime: 500, endTime: 1500 },
    zoomOut: { enabled: false, duration: 500, easing: 'ease-out' as const },
    zoomArea: { x: 0.25, y: 0.25, width: 0.5, height: 0.5 },
    position: { x: 0.5, y: 0.5 },
    cursorFollow: {
      enabled: true,
      areaSize: 0.3,
      smoothing: 0.5,
      edgePadding: 0.1,
      movementThreshold: 0.01,
      showCursorTrail: true,
      zoomScale: 2.5,
    },
  };

  const mockZoomTiming = { startTime: 500, endTime: 1500 };

  // Animation loop for demo
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentTime(prev => {
        const next = prev + 50;
        return next > 2000 ? 0 : next;
      });
    }, 50);

    return () => clearInterval(interval);
  }, [isPlaying]);

  // Calculate zoom results for both modes
  const zoomWithCursor = calculateZoomScale({
    currentPosition: currentTime,
    isFrameBased: false,
    zoomTiming: mockZoomTiming,
    zoomConfig: mockZoomConfig,
    cursorData: mockCursorData,
  });

  const zoomWithoutCursor = calculateZoomScale({
    currentPosition: currentTime,
    isFrameBased: false,
    zoomTiming: mockZoomTiming,
    zoomConfig: { ...mockZoomConfig, cursorFollow: { ...mockZoomConfig.cursorFollow, enabled: false } },
    cursorData: mockCursorData,
  });

  // Get cursor position for visualization
  const cursorPosition = getCursorPositionAtTime(mockCursorData, currentTime);
  const cursorZoomArea = cursorPosition 
    ? calculateCursorZoomArea(cursorPosition, mockZoomConfig.cursorFollow.areaSize, mockZoomConfig.cursorFollow.edgePadding)
    : null;

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Cursor Zooming Demo</CardTitle>
          <CardDescription>
            Interactive demonstration of cursor following zoom functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Controls */}
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => setIsPlaying(!isPlaying)}
              variant={isPlaying ? "destructive" : "default"}
            >
              {isPlaying ? "Stop" : "Play"} Demo
            </Button>
            <div className="flex items-center space-x-2">
              <label htmlFor="timeSlider" className="text-sm">Time:</label>
              <input
                id="timeSlider"
                type="range"
                min="0"
                max="2000"
                step="50"
                value={currentTime}
                onChange={(e) => setCurrentTime(parseInt(e.target.value))}
                className="w-32"
              />
              <span className="text-sm font-mono">{currentTime}ms</span>
            </div>
          </div>

          {/* Visual Demo Area */}
          <div className="grid grid-cols-2 gap-4">
            {/* Without Cursor Following */}
            <div className="space-y-2">
              <h3 className="text-sm font-semibold">Standard Zoom</h3>
              <div 
                className="relative w-full h-32 bg-gray-100 border rounded overflow-hidden"
                style={{
                  transform: `scale(${zoomWithoutCursor.zoomScale})`,
                  transformOrigin: '50% 50%',
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-200 to-blue-400">
                  <div className="absolute top-2 left-2 text-xs">Standard Zoom Area</div>
                  {/* Fixed zoom area indicator */}
                  <div 
                    className="absolute border-2 border-red-500 bg-red-200 bg-opacity-30"
                    style={{
                      left: `${mockZoomConfig.zoomArea.x * 100}%`,
                      top: `${mockZoomConfig.zoomArea.y * 100}%`,
                      width: `${mockZoomConfig.zoomArea.width * 100}%`,
                      height: `${mockZoomConfig.zoomArea.height * 100}%`,
                    }}
                  />
                </div>
              </div>
              <div className="text-xs space-y-1">
                <div>Scale: {zoomWithoutCursor.zoomScale.toFixed(2)}x</div>
                <div>Active: {zoomWithoutCursor.isZoomActive ? 'Yes' : 'No'}</div>
                <div>Phase: {zoomWithoutCursor.phase}</div>
              </div>
            </div>

            {/* With Cursor Following */}
            <div className="space-y-2">
              <h3 className="text-sm font-semibold">Cursor Following Zoom</h3>
              <div 
                className="relative w-full h-32 bg-gray-100 border rounded overflow-hidden"
                style={{
                  transform: `scale(${zoomWithCursor.zoomScale})`,
                  transformOrigin: zoomWithCursor.dynamicZoomArea 
                    ? `${(zoomWithCursor.dynamicZoomArea.x + zoomWithCursor.dynamicZoomArea.width / 2) * 100}% ${(zoomWithCursor.dynamicZoomArea.y + zoomWithCursor.dynamicZoomArea.height / 2) * 100}%`
                    : '50% 50%',
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-green-200 to-green-400">
                  <div className="absolute top-2 left-2 text-xs">Cursor Following</div>
                  
                  {/* Cursor position indicator */}
                  {cursorPosition && (
                    <div 
                      className="absolute w-2 h-2 bg-red-600 rounded-full transform -translate-x-1 -translate-y-1"
                      style={{
                        left: `${cursorPosition.x * 100}%`,
                        top: `${cursorPosition.y * 100}%`,
                      }}
                    />
                  )}
                  
                  {/* Dynamic zoom area indicator */}
                  {cursorZoomArea && (
                    <div 
                      className="absolute border-2 border-green-600 bg-green-300 bg-opacity-30"
                      style={{
                        left: `${cursorZoomArea.x * 100}%`,
                        top: `${cursorZoomArea.y * 100}%`,
                        width: `${cursorZoomArea.width * 100}%`,
                        height: `${cursorZoomArea.height * 100}%`,
                      }}
                    />
                  )}
                </div>
              </div>
              <div className="text-xs space-y-1">
                <div>Scale: {zoomWithCursor.zoomScale.toFixed(2)}x</div>
                <div>Active: {zoomWithCursor.isZoomActive ? 'Yes' : 'No'}</div>
                <div>Following: {zoomWithCursor.isCursorFollowing ? 'Yes' : 'No'}</div>
                <div>Phase: {zoomWithCursor.phase}</div>
                {cursorPosition && (
                  <div>Cursor: ({cursorPosition.x.toFixed(2)}, {cursorPosition.y.toFixed(2)})</div>
                )}
              </div>
            </div>
          </div>

          {/* Status Information */}
          <div className="p-3 bg-muted rounded-md">
            <h4 className="text-sm font-semibold mb-2">Current Status</h4>
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <strong>Timeline:</strong>
                <div>Current: {currentTime}ms</div>
                <div>Zoom Start: {mockZoomTiming.startTime}ms</div>
                <div>Zoom End: {mockZoomTiming.endTime}ms</div>
              </div>
              <div>
                <strong>Cursor Data:</strong>
                <div>Points: {mockCursorData.cursorPoints.length}</div>
                <div>Confidence: {cursorPosition?.confidence.toFixed(2) || 'N/A'}</div>
                <div>Velocity: {cursorPosition ? `(${cursorPosition.velocity.x.toFixed(2)}, ${cursorPosition.velocity.y.toFixed(2)})` : 'N/A'}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
