import React, { useC<PERSON>back, useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ProgressiveFileUploader } from "@/components/ui/progressive-file-uploader";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import ColorPicker from "@/components/color-picker";
import { dispatch } from "@designcombo/events";
import { ADD_VIDEO, ADD_AUDIO } from "@designcombo/state";
import { generateId } from "@designcombo/timeline";
import { IVideo, IAudio } from "@designcombo/types";
import { useLocalVideosStore, LocalVideo } from "../store/use-local-videos-store";
import { useLocalAudiosStore, LocalAudio } from "../store/use-local-audios-store";
import { useLocalImagesStore, LocalImage } from "../store/use-local-images-store";
import { useGlobalFileDrag } from "../hooks/use-global-file-drag";
import { useCanvasStore, DEFAULT_COLORS, DEFAULT_GRADIENTS, BackgroundType } from "../store/use-canvas-store";
import Draggable from "@/components/shared/draggable";
import { Trash2, Upload, Play, Pause, Volume2, Plus, Palette, Image, Blend, RotateCcw, Move } from "lucide-react";

// Color palette for zoom effects (matching timeline colors)
const ZOOM_EFFECT_COLORS = [
  { bg: 'bg-blue-500', name: 'Blue' },
  { bg: 'bg-green-500', name: 'Green' },
  { bg: 'bg-purple-500', name: 'Purple' },
  { bg: 'bg-pink-500', name: 'Pink' },
  { bg: 'bg-indigo-500', name: 'Indigo' },
  { bg: 'bg-teal-500', name: 'Teal' },
  { bg: 'bg-amber-500', name: 'Amber' },
  { bg: 'bg-rose-500', name: 'Rose' },
];

// Function to get color for a zoom effect based on its index
const getZoomEffectColor = (index: number) => {
  return ZOOM_EFFECT_COLORS[index % ZOOM_EFFECT_COLORS.length];
};
import { Icons } from "@/components/shared/icons";
import { getCurrentTime } from "../utils/time";
import useStore from "../store/use-store";
import { useZoomStore } from "../store/use-zoom-store";
import { ExtensionIntegrationPanel } from "../components/extension-integration-panel";
import { CursorOverlayData } from "../../../remotion/CursorOverlay";

export const LocalMedia = () => {
  const { videos, isLoading: videosLoading, actions: videoActions } = useLocalVideosStore();
  const { audios, isLoading: audiosLoading, actions: audioActions } = useLocalAudiosStore();
  const { images, isLoading: imagesLoading, actions: imageActions } = useLocalImagesStore();
  const { isDraggingFiles, hasVideoFiles, hasAudioFiles, resetDragState } = useGlobalFileDrag();
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const [playingAudioId, setPlayingAudioId] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [shadowSettingsExpanded, setShadowSettingsExpanded] = useState<boolean>(false);
  const [blurSettingsExpanded, setBlurSettingsExpanded] = useState<boolean>(false);
  const videoFileInputRef = React.useRef<HTMLInputElement>(null);
  const audioFileInputRef = React.useRef<HTMLInputElement>(null);
  const imageFileInputRef = React.useRef<HTMLInputElement>(null);

  // Canvas store for background settings
  const {
    settings,
    setBackgroundType,
    setSolidColor,
    setGradient,
    setBackgroundImage,
    setPadding,
    setBlurEnabled,
    setBlurIntensity,
    setVideoBorderRadius,

    setVideoBackgroundShadowEnabled,
    setVideoBackgroundShadowX,
    setVideoBackgroundShadowY,
    setVideoBackgroundShadowBlur,
    setVideoBackgroundShadowSpread,
    setVideoBackgroundShadowColor,
    resetSettings,
  } = useCanvasStore();

  // Zoom store for zoom controls
  const {
    zoomEffects,
    addZoomEffect,
    selectZoomEffect,
    selectedZoomEffectId,
    updateZoomEffect,
    isZoomPositioningActive,
    setIsZoomPositioningActive,
    playerRef,
    setCursorOverlayData,
    setShowCursorOverlay
  } = useStore();

  const { config: zoomConfig, resetZoomArea } = useZoomStore();

  const isLoading = videosLoading || audiosLoading || imagesLoading;
  const hasMedia = videos.length > 0 || audios.length > 0 || images.length > 0;

  // Initialize preloaded images on component mount
  useEffect(() => {
    imageActions.initializePreloadedImages();
  }, [imageActions]);

  const handleFileUpload = useCallback(
    async (files: File[]) => {
      // Clear previous errors
      setUploadErrors([]);
      let hasSuccessfulUploads = false;

      for (const file of files) {
        if (file.type.startsWith("video/")) {
          try {
            await videoActions.addVideo(file);
            hasSuccessfulUploads = true;
          } catch (error) {
            console.error("Failed to add video:", error);
            const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
            const fullErrorMessage = `Failed to upload "${file.name}": ${errorMessage}`;
            setUploadErrors(prev => [...prev, fullErrorMessage]);
          }
        } else if (file.type.startsWith("audio/")) {
          try {
            await audioActions.addAudio(file);
            hasSuccessfulUploads = true;
          } catch (error) {
            console.error("Failed to add audio:", error);
            const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
            const fullErrorMessage = `Failed to upload "${file.name}": ${errorMessage}`;
            setUploadErrors(prev => [...prev, fullErrorMessage]);
          }
        } else if (file.type.startsWith("image/")) {
          try {
            await imageActions.addImage(file);
            hasSuccessfulUploads = true;
          } catch (error) {
            console.error("Failed to add image:", error);
            const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
            const fullErrorMessage = `Failed to upload "${file.name}": ${errorMessage}`;
            setUploadErrors(prev => [...prev, fullErrorMessage]);
          }
        }
      }

      // Reset drag state after successful uploads
      if (hasSuccessfulUploads) {
        resetDragState();
      }
    },
    [videoActions, audioActions, resetDragState]
  );

  const handleAddVideoToTimeline = useCallback(
    (localVideo: LocalVideo) => {
      const videoData: Partial<IVideo> = {
        id: generateId(),
        details: {
          src: localVideo.objectUrl,
          width: localVideo.width,
          height: localVideo.height,
          blur: 0,
          brightness: 100,
          flipX: false,
          flipY: false,
          rotate: "0",
          visibility: "visible",
        },
        type: "video",
        metadata: {
          previewUrl: localVideo.thumbnailUrl || localVideo.objectUrl,
          localVideoId: localVideo.id,
          fileName: localVideo.name,
        },
        duration: localVideo.duration,
      };

      dispatch(ADD_VIDEO, {
        payload: videoData,
        options: {
          resourceId: "main",
          scaleMode: "fit",
        },
      });
    },
    []
  );

  const handleAddAudioToTimeline = useCallback(
    (localAudio: LocalAudio) => {
      const audioData: Partial<IAudio> = {
        id: generateId(),
        details: {
          src: localAudio.objectUrl,
          volume: 100,
        },
        type: "audio",
        metadata: {
          localAudioId: localAudio.id,
          fileName: localAudio.name,
          waveformData: localAudio.waveformData,
        },
        duration: localAudio.duration,
      };

      dispatch(ADD_AUDIO, {
        payload: audioData,
        options: {
          resourceId: "audio",
        },
      });
    },
    []
  );

  const handleRemoveVideo = useCallback(
    (id: string, event: React.MouseEvent) => {
      event.stopPropagation();
      videoActions.removeVideo(id);
    },
    [videoActions]
  );

  const handleRemoveAudio = useCallback(
    (id: string, event: React.MouseEvent) => {
      event.stopPropagation();
      // Stop playing if this audio is currently playing
      if (playingAudioId === id && currentAudio) {
        currentAudio.pause();
        setCurrentAudio(null);
        setPlayingAudioId(null);
      }
      audioActions.removeAudio(id);
    },
    [audioActions, playingAudioId, currentAudio]
  );

  const handleAddImageToTimeline = useCallback(
    (localImage: LocalImage) => {
      // For now, we'll set the image as background
      // In the future, this could add the image as a layer to the timeline
      setBackgroundImage(localImage.file || null, localImage.objectUrl);
      setBackgroundType("image");
    },
    [setBackgroundImage, setBackgroundType]
  );

  // Auto-select first image when images are loaded and background type is image but no image is selected
  useEffect(() => {
    if (
      settings.background.type === "image" &&
      images.length > 0 &&
      !settings.background.imageObjectUrl
    ) {
      const firstImage = images[0];
      handleAddImageToTimeline(firstImage);
    }
  }, [images, settings.background.type, settings.background.imageObjectUrl, handleAddImageToTimeline]);

  const handleRemoveLocalImage = useCallback(
    (id: string, event: React.MouseEvent) => {
      event.stopPropagation();
      imageActions.removeImage(id);
    },
    [imageActions]
  );

  const handlePlayPause = useCallback(
    (localAudio: LocalAudio, event: React.MouseEvent) => {
      event.stopPropagation();

      if (playingAudioId === localAudio.id && currentAudio) {
        // Pause current audio
        currentAudio.pause();
        setCurrentAudio(null);
        setPlayingAudioId(null);
      } else {
        // Stop any currently playing audio
        if (currentAudio) {
          currentAudio.pause();
        }

        // Play new audio
        const audio = new Audio(localAudio.objectUrl);
        audio.onended = () => {
          setCurrentAudio(null);
          setPlayingAudioId(null);
        };

        audio.play().then(() => {
          setCurrentAudio(audio);
          setPlayingAudioId(localAudio.id);
        }).catch((error) => {
          console.error("Error playing audio:", error);
        });
      }
    },
    [playingAudioId, currentAudio]
  );

  const clearErrors = useCallback(() => {
    setUploadErrors([]);
  }, []);

  const handleVideoUploadClick = useCallback(() => {
    videoFileInputRef.current?.click();
  }, []);

  const handleAudioUploadClick = useCallback(() => {
    audioFileInputRef.current?.click();
  }, []);

  const handleImageUploadClick = useCallback(() => {
    imageFileInputRef.current?.click();
  }, []);

  const handleVideoFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (files && files.length > 0) {
        const videoFiles = Array.from(files).filter(file => file.type.startsWith("video/"));
        if (videoFiles.length > 0) {
          handleFileUpload(videoFiles);
        }
      }
      // Reset the input value so the same file can be selected again
      event.target.value = '';
    },
    [handleFileUpload]
  );

  const handleAudioFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (files && files.length > 0) {
        const audioFiles = Array.from(files).filter(file => file.type.startsWith("audio/"));
        if (audioFiles.length > 0) {
          handleFileUpload(audioFiles);
        }
      }
      // Reset the input value so the same file can be selected again
      event.target.value = '';
    },
    [handleFileUpload]
  );

  const handleImageFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (files && files.length > 0) {
        const imageFiles = Array.from(files).filter(file => file.type.startsWith("image/"));
        if (imageFiles.length > 0) {
          handleFileUpload(imageFiles);
        }
      }
      // Reset the input value so the same file can be selected again
      event.target.value = '';
    },
    [handleFileUpload]
  );

  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Canvas background handlers
  const handleImageUpload = (files: File[]) => {
    if (files.length > 0) {
      const file = files[0];
      const objectUrl = URL.createObjectURL(file);
      setBackgroundImage(file, objectUrl);
      setBackgroundType("image");
    }
  };



  const handlePaddingChange = (value: number[]) => {
    setPadding(value[0]);
  };

  const handleBlurChange = (value: number[]) => {
    setBlurIntensity(value[0]);
  };

  const handleBorderRadiusChange = (value: number[]) => {
    setVideoBorderRadius(value[0]);
  };



  // Video Background Shadow Handlers
  const handleShadowXChange = (value: number[]) => {
    setVideoBackgroundShadowX(value[0]);
  };

  const handleShadowYChange = (value: number[]) => {
    setVideoBackgroundShadowY(value[0]);
  };

  const handleShadowBlurChange = (value: number[]) => {
    setVideoBackgroundShadowBlur(value[0]);
  };

  const handleShadowSpreadChange = (value: number[]) => {
    setVideoBackgroundShadowSpread(value[0]);
  };



  const handleToggleZoomPositioning = () => {
    // Check if there's a selected zoom effect
    if (!selectedZoomEffectId) {
      // If no effect is selected, show a message or auto-select one
      if (zoomEffects.length > 0) {
        // Auto-select the first effect
        selectZoomEffect(zoomEffects[0].id);
      } else {
        // No effects to position
        return;
      }
    }

    // If entering zoom positioning mode, move playhead to the selected effect's start for better positioning
    if (!isZoomPositioningActive && selectedZoomEffectId) {
      const selectedEffect = zoomEffects.find(effect => effect.id === selectedZoomEffectId);
      if (selectedEffect && playerRef?.current) {
        const fps = useStore.getState().fps;
        const startFrame = (selectedEffect.startTime / 1000) * fps;
        playerRef.current.seekTo(startFrame);
      }
    }

    setIsZoomPositioningActive(!isZoomPositioningActive);
  };

  const handleResetZoomArea = () => {
    if (selectedZoomEffectId) {
      // Reset the selected effect's zoom area to center
      const defaultZoomArea = {
        x: 0.25,
        y: 0.25,
        width: 0.5,
        height: 0.5
      };
      updateZoomEffect(selectedZoomEffectId, { zoomArea: defaultZoomArea });
    } else {
      // Fallback to global reset if no effect is selected
      resetZoomArea();
    }
  };

  // Add zoom effect at current playhead location
  const handleAddZoomEffect = () => {
    const currentTime = getCurrentTime();
    const defaultDuration = 3000; // 3 seconds duration

    const newEffectId = addZoomEffect({
      startTime: currentTime,
      endTime: currentTime + defaultDuration,
    });

    // Select the newly created effect
    selectZoomEffect(newEffectId);
  };

  return (
    <div className="flex flex-1 flex-col">
      {/* Hidden file inputs for video and audio */}
      <input
        ref={videoFileInputRef}
        type="file"
        accept="video/*,.mp4,.mov,.avi,.mkv,.webm"
        multiple
        onChange={handleVideoFileInputChange}
        className="hidden"
      />
      <input
        ref={audioFileInputRef}
        type="file"
        accept="audio/*,.mp3,.wav,.ogg,.m4a,.aac,.flac"
        multiple
        onChange={handleAudioFileInputChange}
        className="hidden"
      />
      <input
        ref={imageFileInputRef}
        type="file"
        accept="image/*,.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg"
        multiple
        onChange={handleImageFileInputChange}
        className="hidden"
      />

      <ScrollArea className="flex-1">
        <div className="px-4">
          <ProgressiveFileUploader
            onValueChange={handleFileUpload}
            accept={{
              "video/*": [".mp4", ".mov", ".avi", ".mkv", ".webm"],
              "audio/*": [".mp3", ".wav", ".ogg", ".m4a", ".aac", ".flac"],
              "image/*": [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".svg"],
            }}
            maxSize={500 * 1024 * 1024} // 500MB
            maxFileCount={20}
            multiple={true}
            disabled={isLoading}
            emptyMessage="No additional media files - Upload existing videos, audio files, and images if needed"
            showUploaderWhenHasFiles={false}
            hasFiles={true}
          >
            {/* Error Display */}
            {uploadErrors.length > 0 && (
              <div className="mt-3 space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-destructive">Upload Errors:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearErrors}
                    className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
                  >
                    Clear
                  </Button>
                </div>
                {uploadErrors.map((error, index) => (
                  <div key={index} className="flex items-start gap-2 p-2 bg-destructive/10 border border-destructive/20 rounded-md">
                    <Upload className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-destructive">{error}</p>
                  </div>
                ))}
              </div>
            )}
            {/* Extension Integration Panel */}
            <div className="mt-4">
              <ExtensionIntegrationPanel />
            </div>





            {/* Background Settings Section */}
            <div className="mt-4">
              <h3 className="text-sm font-medium text-foreground mb-4">Background</h3>

              {/* Canvas mode is always enabled */}
              <div className="space-y-6">
                  {/* Background Type Tabs */}
                  <div className="space-y-3">
                    <Tabs
                      value={settings.background.type}
                      onValueChange={(value) => {
                        const backgroundType = value as BackgroundType;
                        setBackgroundType(backgroundType);

                        // Auto-select first image when image tab is clicked
                        if (backgroundType === "image" && images.length > 0 && !settings.background.imageObjectUrl) {
                          const firstImage = images[0];
                          handleAddImageToTimeline(firstImage);
                        }
                      }}
                      className="w-full"
                    >
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="image" className="flex items-center gap-1">
                          <Image className="h-3 w-3" />
                          Image
                        </TabsTrigger>
                        <TabsTrigger value="gradient" className="flex items-center gap-1">
                          <Blend className="h-3 w-3" />
                          Gradient
                        </TabsTrigger>
                        <TabsTrigger value="solid" className="flex items-center gap-1">
                          <Palette className="h-3 w-3" />
                          Solid
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="solid" className="space-y-3 mt-4">
                        <div className="space-y-2">
                          <Label className="text-xs text-muted-foreground">Color Picker</Label>
                          <ColorPicker
                            value={settings.background.solidColor}
                            onChange={setSolidColor}
                            solid={true}
                            gradient={false}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label className="text-xs text-muted-foreground">Quick Colors</Label>
                          <div className="grid grid-cols-5 gap-2">
                            {DEFAULT_COLORS.map((color) => (
                              <button
                                key={color}
                                onClick={() => setSolidColor(color)}
                                className={`h-8 w-8 rounded border-2 transition-all hover:scale-110 ${
                                  settings.background.solidColor === color
                                    ? "border-primary ring-2 ring-primary/20"
                                    : "border-border"
                                }`}
                                style={{ backgroundColor: color }}
                                title={color}
                              />
                            ))}
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="gradient" className="space-y-3 mt-4">
                        <div className="space-y-2">
                          <Label className="text-xs text-muted-foreground">Gradient Presets</Label>
                          <div className="grid grid-cols-4 gap-2">
                            {DEFAULT_GRADIENTS.map((gradient, index) => {
                              const gradientStyle = gradient.type === "linear"
                                ? `linear-gradient(${gradient.angle}deg, ${gradient.stops.map(stop => `${stop.color} ${stop.position}%`).join(", ")})`
                                : `radial-gradient(circle, ${gradient.stops.map(stop => `${stop.color} ${stop.position}%`).join(", ")})`;

                              return (
                                <button
                                  key={index}
                                  onClick={() => setGradient(gradient)}
                                  className={`aspect-square w-full rounded border-2 transition-all hover:scale-105 ${
                                    JSON.stringify(settings.background.gradient) === JSON.stringify(gradient)
                                      ? "border-primary ring-2 ring-primary/20"
                                      : "border-border"
                                  }`}
                                  style={{ background: gradientStyle }}
                                />
                              );
                            })}
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="image" className="space-y-3 mt-4">
                        {/* Available Images */}
                        {images.length > 0 && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">Available Images</Label>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleImageUploadClick}
                                className="h-6 w-6 p-0 hover:bg-muted/50"
                                title="Upload more images"
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              {images.map((image) => (
                                <button
                                  key={image.id}
                                  onClick={() => handleAddImageToTimeline(image)}
                                  className={`relative aspect-square w-full rounded border-2 transition-all hover:scale-105 group ${
                                    settings.background.imageObjectUrl === image.objectUrl
                                      ? "border-primary ring-2 ring-primary/20"
                                      : "border-border"
                                  }`}
                                >
                                  <img
                                    src={image.objectUrl}
                                    alt={image.name}
                                    className="h-full w-full rounded object-cover"
                                  />
                                  {!image.isPreloaded && (
                                    <Button
                                      size="sm"
                                      variant="destructive"
                                      className="absolute top-1 right-1 h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                      onClick={(e) => handleRemoveLocalImage(image.id, e)}
                                    >
                                      <Trash2 className="h-2 w-2" />
                                    </Button>
                                  )}

                                </button>
                              ))}
                            </div>
                          </div>
                        )}

                      </TabsContent>
                    </Tabs>
                  </div>

                  {/* Padding, Border Radius, and Blur Controls */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Padding, Border Radius & Blur</Label>
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-3">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label className="text-xs text-muted-foreground">
                              Background Padding
                            </Label>
                            <span className="text-xs text-muted-foreground">
                              {settings.padding.value}{settings.padding.unit}
                            </span>
                          </div>
                          <Slider
                            value={[settings.padding.value]}
                            onValueChange={handlePaddingChange}
                            max={200}
                            min={0}
                            step={1}
                            className="w-full"
                          />
                          <Input
                            type="number"
                            value={settings.padding.value}
                            onChange={(e) => setPadding(Number(e.target.value))}
                            className="h-8 text-xs"
                            min={0}
                            max={200}
                          />
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label className="text-xs text-muted-foreground">
                              Video Border Radius
                            </Label>
                            <span className="text-xs text-muted-foreground">
                              {settings.videoBorderRadius.value}px
                            </span>
                          </div>
                          <Slider
                            value={[settings.videoBorderRadius.value]}
                            onValueChange={handleBorderRadiusChange}
                            max={50}
                            min={0}
                            step={1}
                            className="w-full"
                          />
                          <Input
                            type="number"
                            value={settings.videoBorderRadius.value}
                            onChange={(e) => setVideoBorderRadius(Number(e.target.value))}
                            className="h-8 text-xs"
                            min={0}
                            max={50}
                          />
                        </div>
                      </div>

                      {/* Background Blur Controls */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="blur-enabled" className="text-sm font-medium">
                            Background Blur
                          </Label>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => setBlurSettingsExpanded(!blurSettingsExpanded)}
                              disabled={!settings.blur.enabled}
                            >
                              <Icons.settings className="h-4 w-4" />
                            </Button>
                            <Switch
                              id="blur-enabled"
                              checked={settings.blur.enabled}
                              onCheckedChange={setBlurEnabled}
                            />
                          </div>
                        </div>

                        {settings.blur.enabled && blurSettingsExpanded && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">
                                Blur Intensity
                              </Label>
                              <span className="text-xs text-muted-foreground">
                                {settings.blur.intensity}px
                              </span>
                            </div>
                            <Slider
                              value={[settings.blur.intensity]}
                              onValueChange={handleBlurChange}
                              max={20}
                              min={0}
                              step={1}
                              className="w-full"
                            />
                            <Input
                              type="number"
                              value={settings.blur.intensity}
                              onChange={(e) => setBlurIntensity(Number(e.target.value))}
                              className="h-8 text-xs"
                              min={0}
                              max={20}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Video Background Shadow Controls */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="shadow-enabled" className="text-sm font-medium">
                        Video Shadow
                      </Label>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => setShadowSettingsExpanded(!shadowSettingsExpanded)}
                          disabled={!settings.videoBackgroundShadow.enabled}
                        >
                          <Icons.settings className="h-4 w-4" />
                        </Button>
                        <Switch
                          id="shadow-enabled"
                          checked={settings.videoBackgroundShadow.enabled}
                          onCheckedChange={setVideoBackgroundShadowEnabled}
                        />
                      </div>
                    </div>

                    {settings.videoBackgroundShadow.enabled && shadowSettingsExpanded && (
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">
                                X Offset
                              </Label>
                              <span className="text-xs text-muted-foreground">
                                {settings.videoBackgroundShadow.x}px
                              </span>
                            </div>
                            <Slider
                              value={[settings.videoBackgroundShadow.x]}
                              onValueChange={handleShadowXChange}
                              max={50}
                              min={-50}
                              step={1}
                              className="w-full"
                            />
                            <Input
                              type="number"
                              value={settings.videoBackgroundShadow.x}
                              onChange={(e) => setVideoBackgroundShadowX(Number(e.target.value))}
                              className="h-8 text-xs"
                              min={-50}
                              max={50}
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">
                                Y Offset
                              </Label>
                              <span className="text-xs text-muted-foreground">
                                {settings.videoBackgroundShadow.y}px
                              </span>
                            </div>
                            <Slider
                              value={[settings.videoBackgroundShadow.y]}
                              onValueChange={handleShadowYChange}
                              max={50}
                              min={-50}
                              step={1}
                              className="w-full"
                            />
                            <Input
                              type="number"
                              value={settings.videoBackgroundShadow.y}
                              onChange={(e) => setVideoBackgroundShadowY(Number(e.target.value))}
                              className="h-8 text-xs"
                              min={-50}
                              max={50}
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">
                                Blur Radius
                              </Label>
                              <span className="text-xs text-muted-foreground">
                                {settings.videoBackgroundShadow.blur}px
                              </span>
                            </div>
                            <Slider
                              value={[settings.videoBackgroundShadow.blur]}
                              onValueChange={handleShadowBlurChange}
                              max={50}
                              min={0}
                              step={1}
                              className="w-full"
                            />
                            <Input
                              type="number"
                              value={settings.videoBackgroundShadow.blur}
                              onChange={(e) => setVideoBackgroundShadowBlur(Number(e.target.value))}
                              className="h-8 text-xs"
                              min={0}
                              max={50}
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">
                                Spread
                              </Label>
                              <span className="text-xs text-muted-foreground">
                                {settings.videoBackgroundShadow.spread}px
                              </span>
                            </div>
                            <Slider
                              value={[settings.videoBackgroundShadow.spread]}
                              onValueChange={handleShadowSpreadChange}
                              max={20}
                              min={0}
                              step={1}
                              className="w-full"
                            />
                            <Input
                              type="number"
                              value={settings.videoBackgroundShadow.spread}
                              onChange={(e) => setVideoBackgroundShadowSpread(Number(e.target.value))}
                              className="h-8 text-xs"
                              min={0}
                              max={20}
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-xs text-muted-foreground">
                            Shadow Color
                          </Label>
                          <ColorPicker
                            value={settings.videoBackgroundShadow.color}
                            onChange={setVideoBackgroundShadowColor}
                            solid={true}
                            gradient={false}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Zoom Effects */}
                  <div className="pt-4 border-t border-border">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Zoom Effects</Label>
                        <span className="text-xs text-muted-foreground">
                          {zoomEffects.length} effect{zoomEffects.length !== 1 ? 's' : ''}
                        </span>
                      </div>

                      {/* Add Zoom Effect Button */}
                      <Button
                        onClick={handleAddZoomEffect}
                        variant="default"
                        size="sm"
                        className="w-full flex items-center gap-2"
                      >
                        <Plus size={14} />
                        Add Zoom Effect at Playhead
                      </Button>

                      {/* Test Cursor Data Button */}
                      <Button
                        onClick={() => {
                          // Create mock cursor data for testing
                          const mockCursorData: CursorOverlayData = {
                            frameData: {
                              0: [{ id: '1', frame: 0, time: 0, x: 0.1, y: 0.1, action: 'move', originalTimestamp: Date.now() }],
                              30: [{ id: '2', frame: 30, time: 1000, x: 0.5, y: 0.5, action: 'move', originalTimestamp: Date.now() + 1000 }],
                              60: [{ id: '3', frame: 60, time: 2000, x: 0.9, y: 0.9, action: 'move', originalTimestamp: Date.now() + 2000 }],
                            },
                            totalFrames: 90,
                            canvasWidth: 1920,
                            canvasHeight: 1080,
                            metadata: {
                              totalPoints: 3,
                              clicks: 0,
                              duration: 2000,
                            },
                          };
                          setCursorOverlayData(mockCursorData);
                          setShowCursorOverlay(true);
                          console.log('✅ Test cursor data loaded!');
                        }}
                        variant="outline"
                        size="sm"
                        className="w-full flex items-center gap-2"
                      >
                        🎯 Load Test Cursor Data
                      </Button>

                      {/* Zoom Effects List */}
                      {zoomEffects.length > 0 && (
                        <div className="space-y-2">
                          <Label className="text-xs text-muted-foreground">Active Effects</Label>
                          <div className="space-y-1 max-h-32 overflow-y-auto">
                            {zoomEffects.map((effect, index) => {
                              const effectColor = getZoomEffectColor(index);
                              const isSelected = selectedZoomEffectId === effect.id;
                              return (
                                <div
                                  key={effect.id}
                                  onClick={() => selectZoomEffect(effect.id)}
                                  className={`flex items-center justify-between p-2 rounded text-xs cursor-pointer transition-colors ${
                                    isSelected
                                      ? 'bg-blue-500/20 border border-blue-500/40'
                                      : 'bg-muted/50 hover:bg-muted/70'
                                  }`}
                                >
                                  <div className="flex items-center gap-2">
                                    <div className={`w-3 h-3 rounded-full ${effectColor.bg} ${isSelected ? 'ring-2 ring-blue-400' : ''}`} />
                                    <span className={`${isSelected ? 'text-blue-300' : 'text-muted-foreground'}`}>
                                      {effectColor.name} Zoom {isSelected ? '(Selected)' : ''}
                                    </span>
                                  </div>
                                  <span className="text-muted-foreground font-mono">
                                    {(effect.startTime / 1000).toFixed(1)}s - {(effect.endTime / 1000).toFixed(1)}s
                                  </span>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}

                      {/* Zoom Position Section */}
                      <div className="space-y-3">
                        <Label className="text-xs text-muted-foreground">Zoom Position</Label>

                        {selectedZoomEffectId ? (
                          <>
                            {/* Selected Effect Info */}
                            <div className="p-2 bg-blue-500/10 border border-blue-500/20 rounded text-xs">
                              <div className="flex items-center gap-2 mb-1">
                                <div className={`w-2 h-2 rounded-full ${getZoomEffectColor(zoomEffects.findIndex(e => e.id === selectedZoomEffectId)).bg}`} />
                                <span className="text-blue-300 font-medium">
                                  {getZoomEffectColor(zoomEffects.findIndex(e => e.id === selectedZoomEffectId)).name} Zoom Selected
                                </span>
                              </div>
                              <div className="text-muted-foreground">
                                Click "Set Zoom Position" to edit this effect's zoom area
                              </div>
                            </div>

                            {/* Position Display */}
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Label className="text-xs text-muted-foreground">Center Position</Label>
                                <span className="text-xs text-muted-foreground font-mono">
                                  {(() => {
                                    const selectedEffect = zoomEffects.find(e => e.id === selectedZoomEffectId);
                                    const zoomArea = selectedEffect?.zoomArea || zoomConfig.zoomArea;
                                    return `${((zoomArea.x + zoomArea.width / 2) * 100).toFixed(0)}%, ${((zoomArea.y + zoomArea.height / 2) * 100).toFixed(0)}%`;
                                  })()}
                                </span>
                              </div>
                            </div>

                            {/* Position Control Toggle */}
                            <Button
                              onClick={handleToggleZoomPositioning}
                              variant={isZoomPositioningActive ? "default" : "outline"}
                              size="sm"
                              className="w-full flex items-center gap-2"
                              title={isZoomPositioningActive ? "Exit zoom positioning mode" : "Enter zoom positioning mode for the selected effect"}
                            >
                              <Move size={14} />
                              {isZoomPositioningActive ? "Stop Positioning" : "Set Zoom Position"}
                            </Button>

                            {/* Reset Position Button */}
                            <Button
                              onClick={handleResetZoomArea}
                              variant="outline"
                              size="sm"
                              className="w-full flex items-center gap-2"
                              title="Reset the selected effect's zoom area to center"
                            >
                              <RotateCcw size={14} />
                              Reset to Center
                            </Button>
                          </>
                        ) : (
                          <div className="p-3 bg-muted/30 border border-muted/50 rounded text-xs text-center text-muted-foreground">
                            <div className="mb-2">No zoom effect selected</div>
                            <div className="text-xs opacity-70">
                              Click on a colored zoom effect in the timeline or sidebar to select it for positioning
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Cursor Following Section */}
                      {selectedZoomEffectId && (
                        <div className="space-y-3 pt-3 border-t border-border">
                          <Label className="text-xs text-muted-foreground">Cursor Following</Label>

                          {(() => {
                            const selectedEffect = zoomEffects.find(e => e.id === selectedZoomEffectId);
                            const isCursorFollowingEnabled = selectedEffect?.cursorFollowing?.enabled || false;

                            return (
                              <>
                                {/* Enable/Disable Cursor Following */}
                                <div className="flex items-center justify-between">
                                  <Label className="text-xs">Follow Cursor</Label>
                                  <input
                                    type="checkbox"
                                    checked={isCursorFollowingEnabled}
                                    onChange={(e) => {
                                      updateZoomEffect(selectedZoomEffectId, {
                                        cursorFollowing: {
                                          ...selectedEffect?.cursorFollowing,
                                          enabled: e.target.checked,
                                          areaSize: selectedEffect?.cursorFollowing?.areaSize || 0.3,
                                          smoothing: selectedEffect?.cursorFollowing?.smoothing || 0.5,
                                          zoomScale: selectedEffect?.cursorFollowing?.zoomScale || 0,
                                          showCursorTrail: selectedEffect?.cursorFollowing?.showCursorTrail || true,
                                        }
                                      });
                                    }}
                                    className="rounded"
                                  />
                                </div>

                                {isCursorFollowingEnabled && (
                                  <>
                                    {/* Area Size */}
                                    <div className="space-y-1">
                                      <div className="flex items-center justify-between">
                                        <Label className="text-xs">Area Size</Label>
                                        <span className="text-xs text-muted-foreground font-mono">
                                          {((selectedEffect?.cursorFollowing?.areaSize || 0.3) * 100).toFixed(0)}%
                                        </span>
                                      </div>
                                      <input
                                        type="range"
                                        min="0.1"
                                        max="1"
                                        step="0.05"
                                        value={selectedEffect?.cursorFollowing?.areaSize || 0.3}
                                        onChange={(e) => {
                                          updateZoomEffect(selectedZoomEffectId, {
                                            cursorFollowing: {
                                              ...selectedEffect?.cursorFollowing,
                                              enabled: true,
                                              areaSize: parseFloat(e.target.value),
                                            }
                                          });
                                        }}
                                        className="w-full h-1 bg-muted rounded-lg appearance-none cursor-pointer"
                                      />
                                    </div>

                                    {/* Smoothing */}
                                    <div className="space-y-1">
                                      <div className="flex items-center justify-between">
                                        <Label className="text-xs">Smoothing</Label>
                                        <span className="text-xs text-muted-foreground font-mono">
                                          {((selectedEffect?.cursorFollowing?.smoothing || 0.5) * 100).toFixed(0)}%
                                        </span>
                                      </div>
                                      <input
                                        type="range"
                                        min="0"
                                        max="1"
                                        step="0.05"
                                        value={selectedEffect?.cursorFollowing?.smoothing || 0.5}
                                        onChange={(e) => {
                                          updateZoomEffect(selectedZoomEffectId, {
                                            cursorFollowing: {
                                              ...selectedEffect?.cursorFollowing,
                                              enabled: true,
                                              smoothing: parseFloat(e.target.value),
                                            }
                                          });
                                        }}
                                        className="w-full h-1 bg-muted rounded-lg appearance-none cursor-pointer"
                                      />
                                    </div>

                                    {/* Zoom Scale Override */}
                                    <div className="space-y-1">
                                      <div className="flex items-center justify-between">
                                        <Label className="text-xs">Zoom Scale</Label>
                                        <span className="text-xs text-muted-foreground font-mono">
                                          {selectedEffect?.cursorFollowing?.zoomScale || 'Default'}
                                        </span>
                                      </div>
                                      <input
                                        type="range"
                                        min="0"
                                        max="5"
                                        step="0.1"
                                        value={selectedEffect?.cursorFollowing?.zoomScale || 0}
                                        onChange={(e) => {
                                          updateZoomEffect(selectedZoomEffectId, {
                                            cursorFollowing: {
                                              ...selectedEffect?.cursorFollowing,
                                              enabled: true,
                                              zoomScale: parseFloat(e.target.value),
                                            }
                                          });
                                        }}
                                        className="w-full h-1 bg-muted rounded-lg appearance-none cursor-pointer"
                                      />
                                      <div className="text-xs text-muted-foreground">
                                        0 = use default zoom scale
                                      </div>
                                    </div>

                                    {/* Show Cursor Trail */}
                                    <div className="flex items-center justify-between">
                                      <Label className="text-xs">Show Trail</Label>
                                      <input
                                        type="checkbox"
                                        checked={selectedEffect?.cursorFollowing?.showCursorTrail !== false}
                                        onChange={(e) => {
                                          updateZoomEffect(selectedZoomEffectId, {
                                            cursorFollowing: {
                                              ...selectedEffect?.cursorFollowing,
                                              enabled: true,
                                              showCursorTrail: e.target.checked,
                                            }
                                          });
                                        }}
                                        className="rounded"
                                      />
                                    </div>
                                  </>
                                )}

                                <div className="text-xs text-muted-foreground p-2 bg-muted/30 rounded">
                                  {isCursorFollowingEnabled
                                    ? "Zoom will dynamically follow cursor position during this effect"
                                    : "Enable to make zoom follow cursor movements"
                                  }
                                </div>
                              </>
                            );
                          })()}
                        </div>
                      )}
                    </div>
                  </div>


                </div>
            </div>

            {/* Videos Section */}
            <div className="mt-8 pt-6 border-t border-border">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-foreground">Additional Videos</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleVideoUploadClick}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 hover:bg-muted/50"
                  title="Upload existing videos"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
              {videos.length > 0 ? (
                <div className="grid grid-cols-2 gap-2">
                  {videos.map((video) => (
                    <LocalVideoItem
                      key={video.id}
                      video={video}
                      onAddToTimeline={handleAddVideoToTimeline}
                      onRemove={handleRemoveVideo}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-6 text-center text-muted-foreground">
                  <Upload className="h-6 w-6 mb-2 opacity-50" />
                  <p className="text-xs">No additional videos</p>
                  <p className="text-xs opacity-70">Use screen recording for new content</p>
                </div>
              )}
            </div>

            {/* Audio Section */}
            <div className="mt-8 pt-6 border-t border-border">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-foreground">Audio</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleAudioUploadClick}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 hover:bg-muted/50"
                  title="Upload audio"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
              {audios.length > 0 ? (
                <div className="space-y-2">
                  {audios.map((audio) => (
                    <LocalAudioItem
                      key={audio.id}
                      audio={audio}
                      playingAudioId={playingAudioId}
                      onAddToTimeline={handleAddAudioToTimeline}
                      onRemove={handleRemoveAudio}
                      onPlayPause={handlePlayPause}
                      formatDuration={formatDuration}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-6 text-center text-muted-foreground">
                  <Volume2 className="h-6 w-6 mb-2" />
                  <p className="text-xs">No audio files uploaded yet</p>
                </div>
              )}
            </div>

            {/* Reset Button - At the very bottom */}
            <div className="mt-8 pt-6 border-t border-border">
              <Button
                variant="outline"
                size="sm"
                onClick={resetSettings}
                className="w-full flex items-center gap-2"
              >
                <RotateCcw className="h-3 w-3" />
                Reset to Default
              </Button>
            </div>

            {/* Drop Zone */}
            {isDraggingFiles && (hasVideoFiles || hasAudioFiles) && (
              <MediaDropZoneGridItem onDrop={handleFileUpload} />
            )}
          </ProgressiveFileUploader>
        </div>
      </ScrollArea>
    </div>
  );
};

interface LocalVideoItemProps {
  video: LocalVideo;
  onAddToTimeline: (video: LocalVideo) => void;
  onRemove: (id: string, event: React.MouseEvent) => void;
}

const LocalVideoItem: React.FC<LocalVideoItemProps> = ({
  video,
  onAddToTimeline,
  onRemove,
}) => {
  const thumbnailUrl = video.thumbnailUrl || video.objectUrl;

  const dragPreviewStyle = React.useMemo(
    () => ({
      backgroundImage: `url(${thumbnailUrl})`,
      backgroundSize: "cover",
      backgroundPosition: "center",
      width: "120px",
      height: `${120 / video.aspectRatio}px`,
    }),
    [thumbnailUrl, video.aspectRatio]
  );

  return (
    <div className="relative group">
      <Draggable
        data={{
          ...video,
          src: video.objectUrl,
          details: {
            src: video.objectUrl,
            width: video.width,
            height: video.height,
            blur: 0,
            brightness: 100,
            flipX: false,
            flipY: false,
            rotate: "0",
            visibility: "visible",
          },
          metadata: {
            previewUrl: thumbnailUrl,
            localVideoId: video.id,
            fileName: video.name,
          },
        }}
        renderCustomPreview={
          <div
            style={dragPreviewStyle}
            className="draggable rounded-md shadow-lg border border-border"
          />
        }
        shouldDisplayPreview={true}
      >
        <div
          onClick={() => onAddToTimeline(video)}
          className="flex flex-col items-center justify-center overflow-hidden bg-background pb-2 cursor-pointer hover:bg-muted/50 transition-colors rounded-md"
        >
          <div className="relative w-full aspect-video">
            <img
              draggable={false}
              src={thumbnailUrl}
              className="h-full w-full rounded-md object-cover"
              alt={video.name}
            />
            <Button
              size="sm"
              variant="destructive"
              className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => onRemove(video.id, e)}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
          <div className="w-full px-1 mt-1">
            <p className="text-xs text-muted-foreground truncate" title={video.name}>
              {video.name}
            </p>
            <p className="text-xs text-muted-foreground">
              {Math.round(video.duration / 1000)}s
            </p>
          </div>
        </div>
      </Draggable>
    </div>
  );
};

interface LocalAudioItemProps {
  audio: LocalAudio;
  playingAudioId: string | null;
  onAddToTimeline: (audio: LocalAudio) => void;
  onRemove: (id: string, event: React.MouseEvent) => void;
  onPlayPause: (audio: LocalAudio, event: React.MouseEvent) => void;
  formatDuration: (duration: number) => string;
}

const LocalAudioItem: React.FC<LocalAudioItemProps> = ({
  audio,
  playingAudioId,
  onAddToTimeline,
  onRemove,
  onPlayPause,
  formatDuration,
}) => {
  return (
    <Draggable
      data={{
        type: "audio",
        ...audio,
        src: audio.objectUrl,
        details: {
          src: audio.objectUrl,
          volume: 100,
        },
        metadata: {
          localAudioId: audio.id,
          fileName: audio.name,
          waveformData: audio.waveformData,
        },
      }}
      renderCustomPreview={
        <div className="bg-primary/20 border border-primary/40 rounded-lg p-2 shadow-lg flex items-center justify-center w-16 h-16">
          <Volume2 className="h-8 w-8 text-primary" />
        </div>
      }
      shouldDisplayPreview={true}
    >
      <div className="group relative cursor-pointer rounded-lg border border-border bg-card p-3 transition-colors hover:bg-accent">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={(e) => onPlayPause(audio, e)}
          >
            {playingAudioId === audio.id ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <Volume2 className="h-4 w-4 text-muted-foreground" />
              <p className="truncate text-sm font-medium">
                {audio.name}
              </p>
            </div>
            <p className="text-xs text-muted-foreground">
              {formatDuration(audio.duration)}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100"
              onClick={() => onAddToTimeline(audio)}
            >
              <Upload className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-destructive opacity-0 group-hover:opacity-100"
              onClick={(e) => onRemove(audio.id, e)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Simple waveform visualization */}
        {audio.waveformData && (
          <div className="mt-2 flex h-8 items-center gap-px overflow-hidden">
            {audio.waveformData.slice(0, 50).map((value, index) => (
              <div
                key={index}
                className="bg-primary/60 rounded-sm min-w-[2px] flex-shrink-0"
                style={{
                  height: `${Math.max(value * 100, 2)}%`,
                  width: `calc((100% - ${49}px) / 50)`,
                }}
              />
            ))}
          </div>
        )}
      </div>
    </Draggable>
  );
};



interface MediaDropZoneGridItemProps {
  onDrop: (files: File[]) => void;
}

const MediaDropZoneGridItem: React.FC<MediaDropZoneGridItemProps> = ({ onDrop }) => {
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    // Don't stop propagation on drop so the global drag state can be cleared
    const files = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('video/') || file.type.startsWith('audio/')
    );
    if (files.length > 0) {
      onDrop(files);
    }
  };

  return (
    <div
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      className="flex flex-col items-center justify-center bg-background border-2 border-dashed border-muted-foreground/50 rounded-md p-6 text-center hover:border-primary/50 hover:bg-primary/5 transition-colors min-h-[120px]"
    >
      <div className="rounded-full border border-dashed border-muted-foreground/50 p-3 mb-3">
        <Upload className="h-8 w-8 text-muted-foreground" />
      </div>
      <p className="text-sm text-muted-foreground font-medium mb-1">
        Drop your media files here
      </p>
      <p className="text-xs text-muted-foreground">
        Videos and audio files supported
      </p>
    </div>
  );
};
