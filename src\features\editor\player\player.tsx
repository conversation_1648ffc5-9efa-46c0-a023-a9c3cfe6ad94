import { useEffect, useRef, useMemo, memo } from "react";
import Composition from "./composition";
import { Player as RemotionPlayer, PlayerRef } from "@remotion/player";
import useStore from "../store/use-store";
import { calculateSafeDurationInFrames } from "../utils/webm-metadata";
import { useCanvasStore } from "../store/use-canvas-store";
import { shallow } from "zustand/shallow";

// Memoized Player component that doesn't re-render on frame updates
// This follows Remotion's best practices for performance optimization
const PlayerOnly = memo<{ playerRef: React.RefObject<PlayerRef> }>(({ playerRef }) => {
  const { duration, fps, size, cursorOverlayData, showCursorOverlay } = useStore((state) => ({
    duration: state.duration,
    fps: state.fps,
    size: state.size,
    cursorOverlayData: state.cursorOverlayData,
    showCursorOverlay: state.showCursorOverlay,
  }), shallow);

  // Calculate durationInFrames with enhanced WebM support
  const calculateDurationInFrames = () => {
    const safeDuration = calculateSafeDurationInFrames(duration, fps, 'composition');
    return safeDuration;
  };

  // Keep composition dimensions the same - canvas handles the scaling
  const compositionDimensions = useMemo(() => {
    return { width: size.width, height: size.height };
  }, [size]);

  // Memoize input props to prevent unnecessary re-renders
  const inputProps = useMemo(() => ({
    cursorOverlayData,
    showCursorOverlay,
  }), [cursorOverlayData, showCursorOverlay]);

  return (
    <RemotionPlayer
      ref={playerRef}
      component={Composition}
      durationInFrames={calculateDurationInFrames()}
      compositionWidth={compositionDimensions.width}
      compositionHeight={compositionDimensions.height}
      className="h-full w-full"
      fps={30}
      overflowVisible
      inputProps={inputProps}
      // Disable some events during dragging for better performance
    />
  );
});

PlayerOnly.displayName = "PlayerOnly";

const Player = () => {
  const playerRef = useRef<PlayerRef>(null);
  const { setPlayerRef } = useStore((state) => ({
    setPlayerRef: state.setPlayerRef,
  }));

  useEffect(() => {
    setPlayerRef(playerRef);
  }, [setPlayerRef]);

  return <PlayerOnly playerRef={playerRef} />;
};

export default Player;
