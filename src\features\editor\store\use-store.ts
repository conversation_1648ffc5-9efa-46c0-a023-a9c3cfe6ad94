import Timeline from "@designcombo/timeline";
import {
  ISize,
  ITimelineScaleState,
  ITimelineScrollState,
  ITrack,
  ITrackItem,
  ITransition,
} from "@designcombo/types";
import Moveable from "@interactify/moveable";
import { PlayerRef } from "@remotion/player";
import { create } from "zustand";

interface IZoomEffect {
  id: string;        // unique identifier for the zoom effect
  startTime: number; // in milliseconds
  endTime: number;   // in milliseconds
  maxZoomScale?: number; // optional override for zoom scale
  zoomArea?: {       // optional override for zoom area
    x: number;
    y: number;
    width: number;
    height: number;
  };
  // Cursor following mode settings
  cursorFollowing?: {
    enabled: boolean;           // whether this effect uses cursor following
    areaSize?: number;         // override for cursor area size (0-1)
    smoothing?: number;        // override for cursor smoothing (0-1)
    zoomScale?: number;        // override for cursor zoom scale
    showCursorTrail?: boolean; // whether to show cursor trail during this effect
  };
}

// Keep the old interface for backward compatibility
interface IZoomTiming {
  startTime: number; // in milliseconds
  endTime: number;   // in milliseconds
}

interface ITimelineStore {
  duration: number;
  fps: number;
  scale: ITimelineScaleState;
  scroll: ITimelineScrollState;
  size: ISize;
  tracks: ITrack[];
  trackItemIds: string[];
  transitionIds: string[];
  transitionsMap: Record<string, ITransition>;
  trackItemsMap: Record<string, ITrackItem>;
  trackItemDetailsMap: Record<string, any>;
  activeIds: string[];
  timeline: Timeline | null;
  setTimeline: (timeline: Timeline) => void;
  setScale: (scale: ITimelineScaleState) => void;
  setScroll: (scroll: ITimelineScrollState) => void;
  setSize: (size: ISize) => void;
  playerRef: React.RefObject<PlayerRef> | null;
  setPlayerRef: (playerRef: React.RefObject<PlayerRef> | null) => void;

  sceneMoveableRef: React.RefObject<Moveable> | null;
  setSceneMoveableRef: (ref: React.RefObject<Moveable>) => void;
  setState: (state: any) => Promise<void>;

  // Zoom timing controls (legacy - for backward compatibility)
  zoomTiming: IZoomTiming;
  setZoomStartTime: (time: number) => void;
  setZoomEndTime: (time: number) => void;
  clearZoomTiming: () => void;

  // Multiple zoom effects
  zoomEffects: IZoomEffect[];
  selectedZoomEffectId: string | null;
  addZoomEffect: (effect: Omit<IZoomEffect, 'id'>) => string;
  updateZoomEffect: (id: string, updates: Partial<Omit<IZoomEffect, 'id'>>) => void;
  removeZoomEffect: (id: string) => void;
  selectZoomEffect: (id: string | null) => void;
  getZoomEffectById: (id: string) => IZoomEffect | undefined;
  getActiveZoomEffectsAtTime: (time: number) => IZoomEffect[];
  getOverlappingZoomEffects: (effectId: string) => IZoomEffect[];
  hasZoomEffectConflicts: (effectId: string) => boolean;
  getAllZoomEffectConflicts: () => { effectId: string; overlappingWith: string[] }[];

  // Playhead dragging state for performance optimization
  isPlayheadDragging: boolean;
  setIsPlayheadDragging: (isDragging: boolean) => void;

  // Video selection state for performance optimization
  isVideoSelecting: boolean;
  setIsVideoSelecting: (isSelecting: boolean) => void;

  // Zoom positioning overlay state
  isZoomPositioningActive: boolean;
  setIsZoomPositioningActive: (isActive: boolean) => void;
}

const useStore = create<ITimelineStore>((set) => ({
  size: {
    width: 1080,
    height: 1920,
  },

  timeline: null,
  duration: 1000,
  fps: 30,
  scale: {
    // 1x distance (second 0 to second 5, 5 segments).
    index: 7,
    unit: 300,
    zoom: 1 / 300,
    segments: 5,
  },
  scroll: {
    left: 0,
    top: 0,
  },
  playerRef: null,
  trackItemDetailsMap: {},
  activeIds: [],
  targetIds: [],
  tracks: [],
  trackItemIds: [],
  transitionIds: [],
  transitionsMap: {},
  trackItemsMap: {},
  sceneMoveableRef: null,

  // Zoom timing state - no default zoom effect (user must manually add)
  zoomTiming: {
    startTime: 0, // No default start time
    endTime: 0,   // No default end time
  },

  // Multiple zoom effects state
  zoomEffects: [],
  selectedZoomEffectId: null,

  // Playhead dragging state for performance optimization
  isPlayheadDragging: false,

  // Video selection state for performance optimization
  isVideoSelecting: false,

  // Zoom positioning overlay state
  isZoomPositioningActive: false,

  setTimeline: (timeline: Timeline) =>
    set(() => ({
      timeline: timeline,
    })),
  setScale: (scale: ITimelineScaleState) =>
    set(() => ({
      scale: scale,
    })),
  setScroll: (scroll: ITimelineScrollState) =>
    set(() => ({
      scroll: scroll,
    })),
  setSize: (size: ISize) =>
    set(() => ({
      size: size,
    })),
  setState: async (state) => {
    return set({ ...state });
  },
  setPlayerRef: (playerRef: React.RefObject<PlayerRef> | null) =>
    set({ playerRef }),
  setSceneMoveableRef: (ref) => set({ sceneMoveableRef: ref }),

  // Zoom timing methods
  setZoomStartTime: (time: number) =>
    set((state) => ({
      zoomTiming: { ...state.zoomTiming, startTime: time }
    })),
  setZoomEndTime: (time: number) =>
    set((state) => ({
      zoomTiming: { ...state.zoomTiming, endTime: time }
    })),
  clearZoomTiming: () =>
    set(() => ({
      zoomTiming: { startTime: 0, endTime: 0 }
    })),

  // Playhead dragging methods
  setIsPlayheadDragging: (isDragging: boolean) =>
    set({ isPlayheadDragging: isDragging }),

  // Video selection methods
  setIsVideoSelecting: (isSelecting: boolean) =>
    set({ isVideoSelecting: isSelecting }),

  // Zoom positioning methods
  setIsZoomPositioningActive: (isActive: boolean) =>
    set({ isZoomPositioningActive: isActive }),

  // Multiple zoom effects methods
  addZoomEffect: (effect: Omit<IZoomEffect, 'id'>) => {
    const id = `zoom-effect-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newEffect: IZoomEffect = { ...effect, id };

    set((state) => ({
      zoomEffects: [...state.zoomEffects, newEffect],
      selectedZoomEffectId: id, // Auto-select the newly created effect
    }));

    return id;
  },

  updateZoomEffect: (id: string, updates: Partial<Omit<IZoomEffect, 'id'>>) =>
    set((state) => ({
      zoomEffects: state.zoomEffects.map(effect =>
        effect.id === id ? { ...effect, ...updates } : effect
      )
    })),

  removeZoomEffect: (id: string) =>
    set((state) => ({
      zoomEffects: state.zoomEffects.filter(effect => effect.id !== id),
      selectedZoomEffectId: state.selectedZoomEffectId === id ? null : state.selectedZoomEffectId
    })),

  selectZoomEffect: (id: string | null) =>
    set({ selectedZoomEffectId: id }),

  getZoomEffectById: (id: string) => {
    const state = useStore.getState();
    return state.zoomEffects.find(effect => effect.id === id);
  },

  getActiveZoomEffectsAtTime: (time: number) => {
    const state = useStore.getState();
    return state.zoomEffects.filter(effect =>
      time >= effect.startTime && time <= effect.endTime
    );
  },

  getOverlappingZoomEffects: (effectId: string) => {
    const state = useStore.getState();
    const targetEffect = state.zoomEffects.find(effect => effect.id === effectId);
    if (!targetEffect) return [];

    return state.zoomEffects.filter(effect =>
      effect.id !== effectId &&
      !(effect.endTime <= targetEffect.startTime || effect.startTime >= targetEffect.endTime)
    );
  },

  hasZoomEffectConflicts: (effectId: string) => {
    const state = useStore.getState();
    return state.getOverlappingZoomEffects(effectId).length > 0;
  },

  getAllZoomEffectConflicts: () => {
    const state = useStore.getState();
    const conflicts: { effectId: string; overlappingWith: string[] }[] = [];

    state.zoomEffects.forEach(effect => {
      const overlapping = state.getOverlappingZoomEffects(effect.id);
      if (overlapping.length > 0) {
        conflicts.push({
          effectId: effect.id,
          overlappingWith: overlapping.map(e => e.id)
        });
      }
    });

    return conflicts;
  },
}));

export default useStore;
