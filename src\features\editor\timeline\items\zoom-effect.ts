import { ITimelineItem } from "@designcombo/timeline";
import { ITrackItem } from "@designcombo/types";

/**
 * Zoom Effect Timeline Item
 * 
 * This represents a zoom effect that can be displayed on the timeline.
 * It shows the duration and timing of zoom effects applied to video clips.
 */
export class ZoomEffectItem implements ITimelineItem {
  public id: string;
  public type: string = "zoom-effect";
  public startTime: number;
  public endTime: number;
  public duration: number;
  public trackId: string;
  public details: {
    startTime: number;
    endTime: number;
    maxZoomScale: number;
    zoomArea?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  };

  constructor(data: {
    id: string;
    startTime: number;
    endTime: number;
    trackId: string;
    maxZoomScale?: number;
    zoomArea?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }) {
    this.id = data.id;
    this.startTime = data.startTime;
    this.endTime = data.endTime;
    this.duration = data.endTime - data.startTime;
    this.trackId = data.trackId;
    this.details = {
      startTime: data.startTime,
      endTime: data.endTime,
      maxZoomScale: data.maxZoomScale || 1.5,
      zoomArea: data.zoomArea || {
        x: 0.25,
        y: 0.25,
        width: 0.5,
        height: 0.5
      }
    };
  }

  /**
   * Render the zoom effect item on the timeline canvas
   */
  public render(ctx: CanvasRenderingContext2D, options: {
    x: number;
    y: number;
    width: number;
    height: number;
    isSelected?: boolean;
    isHovered?: boolean;
  }): void {
    const { x, y, width, height, isSelected, isHovered } = options;

    // Background
    ctx.fillStyle = isSelected ? '#3b82f6' : isHovered ? '#60a5fa' : '#1e40af';
    ctx.globalAlpha = 0.3;
    ctx.fillRect(x, y, width, height);
    ctx.globalAlpha = 1;

    // Border
    ctx.strokeStyle = isSelected ? '#3b82f6' : '#1e40af';
    ctx.lineWidth = isSelected ? 2 : 1;
    ctx.strokeRect(x, y, width, height);

    // Zoom icon/text
    ctx.fillStyle = '#ffffff';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    
    // Draw zoom symbol
    ctx.fillText('🔍', centerX, centerY - 5);
    ctx.fillText(`${this.details.maxZoomScale}x`, centerX, centerY + 8);

    // Duration text
    const durationText = `${((this.endTime - this.startTime) / 1000).toFixed(1)}s`;
    ctx.font = '8px Arial';
    ctx.fillText(durationText, centerX, centerY + 18);
  }

  /**
   * Check if a point is inside this item
   */
  public hitTest(x: number, y: number, itemBounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  }): boolean {
    return x >= itemBounds.x && 
           x <= itemBounds.x + itemBounds.width && 
           y >= itemBounds.y && 
           y <= itemBounds.y + itemBounds.height;
  }

  /**
   * Get the bounds of this item for a given time range
   */
  public getBounds(timeToPixel: (time: number) => number, trackY: number, trackHeight: number): {
    x: number;
    y: number;
    width: number;
    height: number;
  } {
    const x = timeToPixel(this.startTime);
    const width = timeToPixel(this.endTime) - x;
    
    return {
      x,
      y: trackY + trackHeight - 20, // Position at bottom of track
      width: Math.max(width, 20), // Minimum width
      height: 16 // Fixed height for zoom effects
    };
  }

  /**
   * Update the timing of this zoom effect
   */
  public updateTiming(startTime: number, endTime: number): void {
    this.startTime = startTime;
    this.endTime = endTime;
    this.duration = endTime - startTime;
    this.details.startTime = startTime;
    this.details.endTime = endTime;
  }

  /**
   * Update the zoom configuration
   */
  public updateZoomConfig(config: {
    maxZoomScale?: number;
    zoomArea?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }): void {
    if (config.maxZoomScale !== undefined) {
      this.details.maxZoomScale = config.maxZoomScale;
    }
    if (config.zoomArea !== undefined) {
      this.details.zoomArea = config.zoomArea;
    }
  }

  /**
   * Clone this zoom effect item
   */
  public clone(): ZoomEffectItem {
    return new ZoomEffectItem({
      id: this.id + '_clone',
      startTime: this.startTime,
      endTime: this.endTime,
      trackId: this.trackId,
      maxZoomScale: this.details.maxZoomScale,
      zoomArea: this.details.zoomArea
    });
  }

  /**
   * Serialize this item to JSON
   */
  public toJSON(): any {
    return {
      id: this.id,
      type: this.type,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.duration,
      trackId: this.trackId,
      details: this.details
    };
  }

  /**
   * Create a ZoomEffectItem from JSON data
   */
  public static fromJSON(data: any): ZoomEffectItem {
    return new ZoomEffectItem({
      id: data.id,
      startTime: data.startTime,
      endTime: data.endTime,
      trackId: data.trackId,
      maxZoomScale: data.details?.maxZoomScale,
      zoomArea: data.details?.zoomArea
    });
  }
}

/**
 * Factory function to create zoom effect items
 */
export function createZoomEffect(data: {
  id?: string;
  startTime: number;
  endTime: number;
  trackId: string;
  maxZoomScale?: number;
  zoomArea?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}): ZoomEffectItem {
  return new ZoomEffectItem({
    id: data.id || `zoom-effect-${Date.now()}`,
    startTime: data.startTime,
    endTime: data.endTime,
    trackId: data.trackId,
    maxZoomScale: data.maxZoomScale,
    zoomArea: data.zoomArea
  });
}

/**
 * Helper function to create a zoom effect from current store state
 */
export function createZoomEffectFromStore(store: any, trackId: string): ZoomEffectItem {
  const { zoomTiming } = store;
  const { config } = store.useZoomStore?.getState() || { config: {} };
  
  return createZoomEffect({
    startTime: zoomTiming.startTime,
    endTime: zoomTiming.endTime,
    trackId,
    maxZoomScale: config.maxZoomScale,
    zoomArea: config.zoomArea
  });
}

export default ZoomEffectItem;
