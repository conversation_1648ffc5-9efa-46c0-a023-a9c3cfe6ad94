import { describe, it, expect } from 'vitest';
import { calculateZoomScale } from '../zoom-calculations';
import { getCursorPositionAtTime, calculateCursorZoomArea } from '../cursor-utils';
import { CursorOverlayData } from '../../../../remotion/CursorOverlay';

describe('Cursor Zooming Feature', () => {
  // Mock cursor data for testing
  const mockCursorData: CursorOverlayData = {
    cursorPoints: [
      { frame: 0, time: 0, x: 0.1, y: 0.1, action: 'move' },
      { frame: 30, time: 1000, x: 0.5, y: 0.5, action: 'move' },
      { frame: 60, time: 2000, x: 0.9, y: 0.9, action: 'move' },
    ],
    showCursor: true,
    cursorSize: 20,
    cursorColor: '#ff0000',
    showTrail: false,
    trailLength: 10,
    trailOpacity: 0.5,
  };

  const mockZoomConfig = {
    maxZoomScale: 2.0,
    bezierControlPoints: { p1: 0.25, p2: 0.1, p3: 0.25, p4: 1.0 },
    defaultTiming: { startTime: 500, endTime: 1500 },
    zoomOut: { enabled: false, duration: 500, easing: 'ease-out' as const },
    zoomArea: { x: 0.25, y: 0.25, width: 0.5, height: 0.5 },
    position: { x: 0.5, y: 0.5 },
    cursorFollow: {
      enabled: true,
      areaSize: 0.3,
      smoothing: 0.5,
      edgePadding: 0.1,
      movementThreshold: 0.01,
      showCursorTrail: true,
      zoomScale: 2.5,
    },
  };

  const mockZoomTiming = { startTime: 500, endTime: 1500 };

  describe('Cursor Position Utilities', () => {
    it('should interpolate cursor position at specific time', () => {
      const position = getCursorPositionAtTime(mockCursorData, 1000);
      
      expect(position).toBeDefined();
      expect(position?.x).toBe(0.5);
      expect(position?.y).toBe(0.5);
      expect(position?.confidence).toBeGreaterThan(0);
    });

    it('should calculate cursor zoom area correctly', () => {
      const cursorPosition = { x: 0.5, y: 0.5, confidence: 1.0, velocity: { x: 0, y: 0 } };
      const zoomArea = calculateCursorZoomArea(cursorPosition, 0.3, 0.1);
      
      expect(zoomArea.x).toBeGreaterThanOrEqual(0);
      expect(zoomArea.y).toBeGreaterThanOrEqual(0);
      expect(zoomArea.width).toBe(0.3);
      expect(zoomArea.height).toBe(0.3);
      expect(zoomArea.x + zoomArea.width).toBeLessThanOrEqual(1);
      expect(zoomArea.y + zoomArea.height).toBeLessThanOrEqual(1);
    });

    it('should handle edge constraints for cursor near boundaries', () => {
      const cursorPosition = { x: 0.05, y: 0.95, confidence: 1.0, velocity: { x: 0, y: 0 } };
      const zoomArea = calculateCursorZoomArea(cursorPosition, 0.3, 0.1);
      
      // Should be constrained within bounds
      expect(zoomArea.x).toBeGreaterThanOrEqual(0);
      expect(zoomArea.y).toBeGreaterThanOrEqual(0);
      expect(zoomArea.x + zoomArea.width).toBeLessThanOrEqual(1);
      expect(zoomArea.y + zoomArea.height).toBeLessThanOrEqual(1);
    });
  });

  describe('Zoom Calculation with Cursor Following', () => {
    it('should calculate zoom scale without cursor following when disabled', () => {
      const configWithoutCursor = {
        ...mockZoomConfig,
        cursorFollow: { ...mockZoomConfig.cursorFollow, enabled: false },
      };

      const result = calculateZoomScale({
        currentPosition: 1000,
        isFrameBased: false,
        zoomTiming: mockZoomTiming,
        zoomConfig: configWithoutCursor,
        cursorData: mockCursorData,
      });

      expect(result.zoomScale).toBeGreaterThan(1);
      expect(result.isZoomActive).toBe(true);
      expect(result.isCursorFollowing).toBe(false);
      expect(result.dynamicZoomArea).toBeUndefined();
      expect(result.cursorPosition).toBeUndefined();
    });

    it('should calculate zoom scale with cursor following when enabled', () => {
      const result = calculateZoomScale({
        currentPosition: 1000,
        isFrameBased: false,
        zoomTiming: mockZoomTiming,
        zoomConfig: mockZoomConfig,
        cursorData: mockCursorData,
      });

      expect(result.zoomScale).toBeGreaterThan(1);
      expect(result.isZoomActive).toBe(true);
      expect(result.isCursorFollowing).toBe(true);
      expect(result.dynamicZoomArea).toBeDefined();
      expect(result.cursorPosition).toBeDefined();
      
      // Should use cursor-specific zoom scale
      expect(result.zoomScale).toBeLessThanOrEqual(1 + mockZoomConfig.cursorFollow.zoomScale);
    });

    it('should not activate cursor following when zoom is inactive', () => {
      const result = calculateZoomScale({
        currentPosition: 100, // Before zoom start time
        isFrameBased: false,
        zoomTiming: mockZoomTiming,
        zoomConfig: mockZoomConfig,
        cursorData: mockCursorData,
      });

      expect(result.zoomScale).toBe(1);
      expect(result.isZoomActive).toBe(false);
      expect(result.isCursorFollowing).toBe(false);
      expect(result.dynamicZoomArea).toBeUndefined();
    });

    it('should handle missing cursor data gracefully', () => {
      const result = calculateZoomScale({
        currentPosition: 1000,
        isFrameBased: false,
        zoomTiming: mockZoomTiming,
        zoomConfig: mockZoomConfig,
        cursorData: null,
      });

      expect(result.zoomScale).toBeGreaterThan(1);
      expect(result.isZoomActive).toBe(true);
      expect(result.isCursorFollowing).toBe(false);
      expect(result.dynamicZoomArea).toBeUndefined();
    });

    it('should handle low confidence cursor positions', () => {
      const lowConfidenceCursorData: CursorOverlayData = {
        ...mockCursorData,
        cursorPoints: [
          { frame: 30, time: 1000, x: 0.5, y: 0.5, action: 'move' },
        ],
      };

      // Mock getCursorPositionAtTime to return low confidence
      const result = calculateZoomScale({
        currentPosition: 1000,
        isFrameBased: false,
        zoomTiming: mockZoomTiming,
        zoomConfig: mockZoomConfig,
        cursorData: lowConfidenceCursorData,
      });

      expect(result.zoomScale).toBeGreaterThan(1);
      expect(result.isZoomActive).toBe(true);
      // Should still work with available cursor data
    });
  });

  describe('Frame-based Calculations', () => {
    it('should work with frame-based calculations', () => {
      const result = calculateZoomScale({
        currentPosition: 30, // Frame 30 at 30fps = 1000ms
        isFrameBased: true,
        fps: 30,
        zoomTiming: mockZoomTiming,
        zoomConfig: mockZoomConfig,
        cursorData: mockCursorData,
      });

      expect(result.zoomScale).toBeGreaterThan(1);
      expect(result.isZoomActive).toBe(true);
      expect(result.isCursorFollowing).toBe(true);
      expect(result.dynamicZoomArea).toBeDefined();
    });

    it('should throw error when fps is missing for frame-based calculations', () => {
      expect(() => {
        calculateZoomScale({
          currentPosition: 30,
          isFrameBased: true,
          // fps missing
          zoomTiming: mockZoomTiming,
          zoomConfig: mockZoomConfig,
          cursorData: mockCursorData,
        });
      }).toThrow('FPS is required when using frame-based calculations');
    });
  });
});
