import { CursorPoint, CursorOverlayData } from '../../../remotion/CursorOverlay';

/**
 * Normalized cursor position (0-1 coordinates)
 */
export interface NormalizedCursorPosition {
  x: number;
  y: number;
  timestamp: number;
  action: 'move' | 'click';
}

/**
 * Cursor position with interpolation metadata
 */
export interface InterpolatedCursorPosition extends NormalizedCursorPosition {
  isInterpolated: boolean;
  confidence: number; // 0-1, how confident we are in this position
}

/**
 * Configuration for cursor position interpolation
 */
export interface CursorInterpolationConfig {
  /** Maximum time gap (ms) to interpolate across */
  maxInterpolationGap: number;
  /** Smoothing factor for interpolation (0 = linear, 1 = smooth) */
  smoothingFactor: number;
  /** Whether to use velocity-based prediction for gaps */
  useVelocityPrediction: boolean;
}

/**
 * Default cursor interpolation configuration
 */
const DEFAULT_CURSOR_INTERPOLATION_CONFIG: CursorInterpolationConfig = {
  maxInterpolationGap: 500, // 500ms max gap
  smoothingFactor: 0.7,     // Moderate smoothing
  useVelocityPrediction: true,
};

/**
 * Get cursor position at a specific time with interpolation
 * 
 * @param cursorData - Cursor overlay data from extension
 * @param timeMs - Time in milliseconds
 * @param config - Interpolation configuration
 * @returns Interpolated cursor position or null if no data available
 */
export function getCursorPositionAtTime(
  cursorData: CursorOverlayData | null,
  timeMs: number,
  config: CursorInterpolationConfig = DEFAULT_CURSOR_INTERPOLATION_CONFIG
): InterpolatedCursorPosition | null {
  if (!cursorData || !cursorData.frameData) {
    return null;
  }

  // Convert all frame data to time-based array for easier processing
  const timeBasedPoints: CursorPoint[] = [];
  Object.values(cursorData.frameData).forEach(framePoints => {
    timeBasedPoints.push(...framePoints);
  });

  // Sort by time
  timeBasedPoints.sort((a, b) => a.time - b.time);

  if (timeBasedPoints.length === 0) {
    return null;
  }

  // Find the closest points before and after the target time
  let beforePoint: CursorPoint | null = null;
  let afterPoint: CursorPoint | null = null;

  for (let i = 0; i < timeBasedPoints.length; i++) {
    const point = timeBasedPoints[i];
    
    if (point.time <= timeMs) {
      beforePoint = point;
    } else {
      afterPoint = point;
      break;
    }
  }

  // If we have an exact match, return it
  if (beforePoint && beforePoint.time === timeMs) {
    return {
      x: beforePoint.x / cursorData.canvasWidth,
      y: beforePoint.y / cursorData.canvasHeight,
      timestamp: beforePoint.time,
      action: beforePoint.action,
      isInterpolated: false,
      confidence: 1.0,
    };
  }

  // If we only have a before point, use it
  if (beforePoint && !afterPoint) {
    return {
      x: beforePoint.x / cursorData.canvasWidth,
      y: beforePoint.y / cursorData.canvasHeight,
      timestamp: beforePoint.time,
      action: beforePoint.action,
      isInterpolated: false,
      confidence: 0.8, // Lower confidence for extrapolation
    };
  }

  // If we only have an after point, use it
  if (!beforePoint && afterPoint) {
    return {
      x: afterPoint.x / cursorData.canvasWidth,
      y: afterPoint.y / cursorData.canvasHeight,
      timestamp: afterPoint.time,
      action: afterPoint.action,
      isInterpolated: false,
      confidence: 0.8, // Lower confidence for extrapolation
    };
  }

  // If we have both points, interpolate
  if (beforePoint && afterPoint) {
    const timeDiff = afterPoint.time - beforePoint.time;
    
    // Check if the gap is too large for reliable interpolation
    if (timeDiff > config.maxInterpolationGap) {
      // Use the closer point
      const beforeDist = timeMs - beforePoint.time;
      const afterDist = afterPoint.time - timeMs;
      
      const closerPoint = beforeDist <= afterDist ? beforePoint : afterPoint;
      return {
        x: closerPoint.x / cursorData.canvasWidth,
        y: closerPoint.y / cursorData.canvasHeight,
        timestamp: closerPoint.time,
        action: closerPoint.action,
        isInterpolated: false,
        confidence: 0.6, // Lower confidence due to large gap
      };
    }

    // Perform interpolation
    const progress = (timeMs - beforePoint.time) / timeDiff;
    const smoothedProgress = applySmoothingFunction(progress, config.smoothingFactor);

    const interpolatedX = beforePoint.x + (afterPoint.x - beforePoint.x) * smoothedProgress;
    const interpolatedY = beforePoint.y + (afterPoint.y - beforePoint.y) * smoothedProgress;

    return {
      x: interpolatedX / cursorData.canvasWidth,
      y: interpolatedY / cursorData.canvasHeight,
      timestamp: timeMs,
      action: beforePoint.action, // Use the action from the before point
      isInterpolated: true,
      confidence: Math.max(0.3, 1.0 - (timeDiff / config.maxInterpolationGap) * 0.7),
    };
  }

  return null;
}

/**
 * Get cursor position at a specific frame
 * 
 * @param cursorData - Cursor overlay data from extension
 * @param frame - Frame number
 * @param fps - Frames per second
 * @param config - Interpolation configuration
 * @returns Interpolated cursor position or null if no data available
 */
export function getCursorPositionAtFrame(
  cursorData: CursorOverlayData | null,
  frame: number,
  fps: number,
  config: CursorInterpolationConfig = DEFAULT_CURSOR_INTERPOLATION_CONFIG
): InterpolatedCursorPosition | null {
  const timeMs = (frame / fps) * 1000;
  return getCursorPositionAtTime(cursorData, timeMs, config);
}

/**
 * Apply smoothing function to interpolation progress
 * 
 * @param progress - Linear progress (0-1)
 * @param smoothingFactor - Smoothing factor (0 = linear, 1 = maximum smoothing)
 * @returns Smoothed progress value
 */
function applySmoothingFunction(progress: number, smoothingFactor: number): number {
  if (smoothingFactor === 0) {
    return progress; // Linear interpolation
  }

  // Use ease-in-out cubic function for smoothing
  const easeInOutCubic = (t: number): number => {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  };

  // Blend between linear and eased progress
  const easedProgress = easeInOutCubic(progress);
  return progress + (easedProgress - progress) * smoothingFactor;
}

/**
 * Calculate cursor velocity at a specific time
 * 
 * @param cursorData - Cursor overlay data from extension
 * @param timeMs - Time in milliseconds
 * @param windowMs - Time window to calculate velocity over (default: 100ms)
 * @returns Cursor velocity in normalized units per second, or null if insufficient data
 */
export function getCursorVelocityAtTime(
  cursorData: CursorOverlayData | null,
  timeMs: number,
  windowMs: number = 100
): { vx: number; vy: number; speed: number } | null {
  if (!cursorData) return null;

  const beforeTime = timeMs - windowMs / 2;
  const afterTime = timeMs + windowMs / 2;

  const beforePos = getCursorPositionAtTime(cursorData, beforeTime);
  const afterPos = getCursorPositionAtTime(cursorData, afterTime);

  if (!beforePos || !afterPos) return null;

  const deltaTime = (afterTime - beforeTime) / 1000; // Convert to seconds
  const deltaX = afterPos.x - beforePos.x;
  const deltaY = afterPos.y - beforePos.y;

  const vx = deltaX / deltaTime;
  const vy = deltaY / deltaTime;
  const speed = Math.sqrt(vx * vx + vy * vy);

  return { vx, vy, speed };
}

/**
 * Check if cursor is near the edges of the video
 * 
 * @param position - Normalized cursor position
 * @param edgePadding - Edge padding (0-1)
 * @returns Object indicating which edges the cursor is near
 */
export function getCursorEdgeProximity(
  position: NormalizedCursorPosition,
  edgePadding: number
): {
  nearLeft: boolean;
  nearRight: boolean;
  nearTop: boolean;
  nearBottom: boolean;
  anyEdge: boolean;
} {
  const nearLeft = position.x < edgePadding;
  const nearRight = position.x > (1 - edgePadding);
  const nearTop = position.y < edgePadding;
  const nearBottom = position.y > (1 - edgePadding);

  return {
    nearLeft,
    nearRight,
    nearTop,
    nearBottom,
    anyEdge: nearLeft || nearRight || nearTop || nearBottom,
  };
}

/**
 * Calculate zoom area centered on cursor position with edge constraints
 * 
 * @param cursorPosition - Normalized cursor position
 * @param areaSize - Size of zoom area (0-1)
 * @param edgePadding - Minimum distance from edges (0-1)
 * @returns Constrained zoom area
 */
export function calculateCursorZoomArea(
  cursorPosition: NormalizedCursorPosition,
  areaSize: number,
  edgePadding: number
): {
  x: number;
  y: number;
  width: number;
  height: number;
} {
  const halfSize = areaSize / 2;
  
  // Calculate ideal center position
  let centerX = cursorPosition.x;
  let centerY = cursorPosition.y;

  // Constrain to stay within bounds with edge padding
  const minCenter = edgePadding + halfSize;
  const maxCenter = 1 - edgePadding - halfSize;

  centerX = Math.max(minCenter, Math.min(maxCenter, centerX));
  centerY = Math.max(minCenter, Math.min(maxCenter, centerY));

  // Calculate final zoom area
  return {
    x: centerX - halfSize,
    y: centerY - halfSize,
    width: areaSize,
    height: areaSize,
  };
}
