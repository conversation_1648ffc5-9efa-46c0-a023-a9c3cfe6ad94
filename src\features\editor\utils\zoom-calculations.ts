import { IZoomConfig, IZoomTiming } from "../store/use-zoom-store";
import { CursorOverlayData } from "../../../remotion/CursorOverlay";
import { getCursorPositionAtTime, calculateCursorZoomArea, InterpolatedCursorPosition } from "./cursor-utils";

/**
 * Cubic bezier helper function for smooth zoom animations
 * This creates smooth acceleration and deceleration curves
 *
 * @param t - Progress value between 0 and 1
 * @param p1 - First control point
 * @param p2 - Second control point
 * @param p3 - Third control point
 * @param p4 - Fourth control point
 * @returns Bezier curve value
 */
export function cubicBezier(t: number, p1: number, p2: number, p3: number, p4: number): number {
  const u = 1 - t;
  const tt = t * t;
  const uu = u * u;
  const uuu = uu * u;
  const ttt = tt * t;
  return 3 * uu * t * p2 + 3 * u * tt * p3 + ttt;
}

/**
 * Easing functions for zoom-out animation
 */
export function easeOut(t: number): number {
  return 1 - Math.pow(1 - t, 3);
}

export function linear(t: number): number {
  return t;
}

/**
 * Parameters for zoom scale calculation
 */
export interface IZoomCalculationParams {
  /** Current time or frame position */
  currentPosition: number;
  /** Whether currentPosition is in frames (true) or milliseconds (false) */
  isFrameBased: boolean;
  /** Frames per second (required if isFrameBased is true) */
  fps?: number;
  /** Zoom timing configuration */
  zoomTiming: IZoomTiming;
  /** Zoom configuration settings */
  zoomConfig: IZoomConfig;
  /** Cursor data for cursor following mode (optional) */
  cursorData?: CursorOverlayData | null;
}

/**
 * Result of zoom scale calculation
 */
export interface IZoomCalculationResult {
  /** The calculated zoom scale (1.0 = no zoom) */
  zoomScale: number;
  /** Whether zoom is currently active */
  isZoomActive: boolean;
  /** Progress through the zoom animation (0-1) */
  progress: number;
  /** Bezier-adjusted progress value */
  bezierProgress: number;
  /** Current zoom phase: 'zoom-in', 'zoom-out', or 'inactive' */
  phase: 'zoom-in' | 'zoom-out' | 'inactive';
  /** Dynamic zoom area (for cursor following mode) */
  dynamicZoomArea?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  /** Current cursor position (if cursor following is active) */
  cursorPosition?: InterpolatedCursorPosition;
  /** Whether cursor following mode was used for this calculation */
  isCursorFollowing?: boolean;
}

/**
 * Centralized zoom scale calculation function
 * This eliminates the duplicated zoom logic between VideoEditorComposition and canvas-container
 * 
 * @param params - Zoom calculation parameters
 * @returns Zoom calculation result
 */
export function calculateZoomScale(params: IZoomCalculationParams): IZoomCalculationResult {
  const { currentPosition, isFrameBased, fps, zoomTiming, zoomConfig, cursorData } = params;

  // Convert position to time in milliseconds if needed
  let currentTime: number;
  if (isFrameBased) {
    if (!fps) {
      throw new Error("FPS is required when using frame-based calculations");
    }
    currentTime = (currentPosition / fps) * 1000;
  } else {
    currentTime = currentPosition;
  }

  // Extract timing values
  const zoomStartTime = zoomTiming.startTime;
  const zoomEndTime = zoomTiming.endTime;
  const zoomDuration = zoomEndTime - zoomStartTime;

  // Early return for performance if outside zoom range
  if (zoomDuration <= 0) {
    return {
      zoomScale: 1,
      isZoomActive: false,
      progress: 0,
      bezierProgress: 0,
      phase: 'inactive'
    };
  }

  // Calculate zoom-out end time
  const zoomOutDuration = zoomConfig.zoomOut.enabled ? zoomConfig.zoomOut.duration : 0;
  const zoomOutEndTime = zoomEndTime + zoomOutDuration;

  // Early return if completely outside zoom range for better performance
  if (currentTime < zoomStartTime && (!zoomConfig.zoomOut.enabled || currentTime > zoomOutEndTime)) {
    return {
      zoomScale: 1,
      isZoomActive: false,
      progress: 0,
      bezierProgress: 0,
      phase: 'inactive'
    };
  }

  // Initialize result
  let zoomScale = 1;
  let isZoomActive = false;
  let progress = 0;
  let bezierProgress = 0;
  let phase: 'zoom-in' | 'zoom-out' | 'inactive' = 'inactive';

  // Check if we're in the zoom-in phase
  if (currentTime >= zoomStartTime && currentTime <= zoomEndTime) {
    isZoomActive = true;
    phase = 'zoom-in';

    // Calculate progress through the zoom-in animation (0 to 1)
    progress = (currentTime - zoomStartTime) / zoomDuration;

    // Use sine wave to create smooth zoom in and out within the duration
    // sin(0) = 0, sin(π/2) = 1, sin(π) = 0
    // This creates a smooth zoom that peaks in the middle and returns to 1.0 at the end
    bezierProgress = Math.sin(progress * Math.PI);

    // Calculate final zoom scale
    zoomScale = 1 + bezierProgress * zoomConfig.maxZoomScale;
  }
  // Optional zoom-out phase: only used if someone wants additional zoom-out after the main cycle
  // Note: The main zoom cycle now completes within the zoom duration using sine wave
  else if (zoomConfig.zoomOut.enabled && zoomOutDuration > 0 &&
           currentTime > zoomEndTime && currentTime <= zoomOutEndTime) {
    isZoomActive = true;
    phase = 'zoom-out';

    // Calculate progress through the zoom-out animation (0 to 1)
    progress = (currentTime - zoomEndTime) / zoomOutDuration;

    // Apply easing function based on configuration
    let easedProgress: number;
    switch (zoomConfig.zoomOut.easing) {
      case 'linear':
        easedProgress = linear(progress);
        break;
      case 'ease-out':
        easedProgress = easeOut(progress);
        break;
      case 'bezier':
        const { p1, p2, p3, p4 } = zoomConfig.bezierControlPoints;
        easedProgress = cubicBezier(progress, p1, p2, p3, p4);
        break;
      default:
        easedProgress = easeOut(progress);
    }

    bezierProgress = easedProgress;

    // Since the main zoom cycle already returned to 1.0, this phase starts from 1.0
    // and can zoom out further if desired (scale < 1.0) or do nothing
    zoomScale = 1.0; // Keep at normal scale since main cycle already completed
  }

  // Handle cursor following mode
  let dynamicZoomArea: { x: number; y: number; width: number; height: number } | undefined;
  let cursorPosition: InterpolatedCursorPosition | undefined;
  let isCursorFollowing = false;

  if (zoomConfig.cursorFollow.enabled && cursorData && isZoomActive) {
    // Get cursor position at current time
    cursorPosition = getCursorPositionAtTime(cursorData, currentTime);

    if (cursorPosition && cursorPosition.confidence > 0.3) {
      isCursorFollowing = true;

      // Calculate dynamic zoom area based on cursor position
      dynamicZoomArea = calculateCursorZoomArea(
        cursorPosition,
        zoomConfig.cursorFollow.areaSize,
        zoomConfig.cursorFollow.edgePadding
      );

      // Use cursor-specific zoom scale if available
      if (zoomConfig.cursorFollow.zoomScale > 0) {
        // Apply cursor zoom scale instead of regular zoom scale
        const cursorZoomScale = zoomConfig.cursorFollow.zoomScale;
        zoomScale = 1 + bezierProgress * cursorZoomScale;
      }
    }
  }

  return {
    zoomScale,
    isZoomActive,
    progress,
    bezierProgress,
    phase,
    dynamicZoomArea,
    cursorPosition,
    isCursorFollowing,
  };
}

/**
 * Convenience function for frame-based zoom calculation (used by Remotion)
 *
 * @param currentFrame - Current frame number
 * @param fps - Frames per second
 * @param zoomTiming - Zoom timing configuration
 * @param zoomConfig - Zoom configuration settings
 * @param cursorData - Optional cursor data for cursor following mode
 * @returns Zoom calculation result
 */
export function calculateZoomScaleFromFrame(
  currentFrame: number,
  fps: number,
  zoomTiming: IZoomTiming,
  zoomConfig: IZoomConfig,
  cursorData?: CursorOverlayData | null
): IZoomCalculationResult {
  return calculateZoomScale({
    currentPosition: currentFrame,
    isFrameBased: true,
    fps,
    zoomTiming,
    zoomConfig,
    cursorData,
  });
}

/**
 * Convenience function for time-based zoom calculation (used by Player)
 *
 * @param currentTime - Current time in milliseconds
 * @param zoomTiming - Zoom timing configuration
 * @param zoomConfig - Zoom configuration settings
 * @param cursorData - Optional cursor data for cursor following mode
 * @returns Zoom calculation result
 */
export function calculateZoomScaleFromTime(
  currentTime: number,
  zoomTiming: IZoomTiming,
  zoomConfig: IZoomConfig,
  cursorData?: CursorOverlayData | null
): IZoomCalculationResult {
  return calculateZoomScale({
    currentPosition: currentTime,
    isFrameBased: false,
    zoomTiming,
    zoomConfig,
    cursorData,
  });
}
